<?php return array(
    'root' => array(
        'name' => 'akaunting/flowmaker',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '9950e337475ebb47aeb14f237d76761a02f29d69',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'akaunting/flowmaker' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '9950e337475ebb47aeb14f237d76761a02f29d69',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'phpoffice/math' => array(
            'pretty_version' => '0.3.0',
            'version' => '0.3.0.0',
            'reference' => 'fc31c8f57a7a81f962cbf389fd89f4d9d06fc99a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpword' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => '6d75328229bc93790b37e93741adf70646cea958',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpword',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'smalot/pdfparser' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => '8440edbf58c8596074e78ada38dcb0bd041a5948',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smalot/pdfparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/browser-kit' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '5384291845e74fd7d54f3d925c4a86ce12336593',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/browser-kit',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dom-crawler' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '0fabbc3d6a9c473b716a93fc8e7a537adb396166',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dom-crawler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '57e4fb86314015a695a750ace358d07a7e37b8a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '75d7043853a42837e68111812f4d964b01e5101c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
