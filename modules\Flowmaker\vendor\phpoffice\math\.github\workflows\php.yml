name: PHP
on: 
    push:
        branches:
            - master
    pull_request:

jobs:
  php-cs-fixer:
    name: <PERSON>HP CS Fixer
    runs-on: ubuntu-latest
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          extensions: xml

      - uses: actions/checkout@v4

      -   name: Validate composer config
          run: composer validate --strict

      -   name: Composer Install
          run: composer global require friendsofphp/php-cs-fixer

      -   name: Add environment path
          run: export PATH="$PATH:$HOME/.composer/vendor/bin"

      -   name: Run PHPCSFixer
          run: php-cs-fixer fix --dry-run --diff

  phpstan:
    name: PHP Static Analysis
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php: 
            - '7.1'
            - '7.2'
            - '7.3'
            - '7.4'
            - '8.0'
            - '8.1'
            - '8.2'
            - '8.3'
            - '8.4'
    steps:
        -   name: Setup PHP
            uses: shivammathur/setup-php@v2
            with:
                php-version: ${{ matrix.php }}
                extensions: xml

        -   uses: actions/checkout@v4

        -   name: Composer Install
            run: composer install --ansi --prefer-dist --no-interaction --no-progress

        -   name: Run phpstan
            run: ./vendor/bin/phpstan analyse -c phpstan.neon.dist

  phpunit:
    name: PHPUnit
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php: 
            - '7.1'
            - '7.2'
            - '7.3'
            - '7.4'
            - '8.0'
            - '8.1'
            - '8.2'
            - '8.3'
            - '8.4'
    steps:
      -   name: Setup PHP
          uses: shivammathur/setup-php@v2
          with:
              php-version: ${{ matrix.php }}
              extensions: xml
              coverage: ${{ (matrix.php == '8.1') && 'xdebug' || 'none' }}

      -   uses: actions/checkout@v4

      -   name: Install dependencies
          run: composer install --ansi --prefer-dist --no-interaction --no-progress

      -   name: Run PHPUnit
          if: matrix.php != '8.1'
          run: ./vendor/bin/phpunit -c phpunit.xml.dist

      -   name: Run PHPUnit (w CodeCoverage)
          if: matrix.php == '8.1'
          run: XDEBUG_MODE=coverage ./vendor/bin/phpunit -c phpunit.xml.dist --coverage-clover build/clover.xml

      -   name: Upload coverage results to Coveralls
          if: matrix.php == '8.1'
          env:
            COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          run: |
            wget https://github.com/php-coveralls/php-coveralls/releases/download/v2.4.3/php-coveralls.phar
            chmod +x php-coveralls.phar
            php php-coveralls.phar --coverage_clover=build/clover.xml --json_path=build/coveralls-upload.json -vvv

  roave-backwards-compatibility-check:
    name: Roave Backwards Compatibility Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: "Check for BC breaks"
        run: docker run -u $(id -u) -v $(pwd):/app nyholm/roave-bc-check-ga
