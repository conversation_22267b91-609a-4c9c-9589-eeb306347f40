# 0.3.0

## Enhancements

- Added support for PHP 8.4 by [@Progi1984](https://github/Progi1984) in [#18](https://github.com/PHPOffice/Math/pull/18)
- MathML Writer : Write Semantics by [@codinglist](https://github/codinglist) & [@Progi1984](https://github/Progi1984) in [#19](https://github.com/PHPOffice/Math/pull/19) & [#20](https://github.com/PHPOffice/Math/pull/20)

## Bug fixes

- Documentation : Added Release 0.3.0 by [@Progi1984](https://github/Progi1984) in [#17](https://github.com/PHPOffice/Math/pull/17)

## Miscellaneous

- N/A

## Security fixes

- Fixed XXE when processing an XML file in the MathML format by <PERSON> (Positive Technologies) & [@Progi1984](https://github/Progi1984) in [GHSA-42hm-pq2f-3r7m](https://github.com/PHPOffice/Math/security/advisories/GHSA-42hm-pq2f-3r7m)