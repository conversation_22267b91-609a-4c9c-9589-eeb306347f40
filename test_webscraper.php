<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Flowmaker\Services\WebsiteScraperService;

// Test the improved web scraping functionality
echo "Testing improved web scraping with https://alviongs.com/\n";
echo "=======================================================\n\n";

try {
    echo "1. Testing improved WebsiteScraperService:\n";

    $scraperService = new WebsiteScraperService();
    $result = $scraperService->extractText('https://alviongs.com/');

    echo "✓ Service completed successfully\n";
    echo "✓ Title: " . $result['title'] . "\n";
    echo "✓ URL: " . $result['url'] . "\n";
    echo "✓ Content length: " . strlen($result['content']) . " characters\n\n";

    echo "2. Full extracted content:\n";
    echo "==========================\n";
    echo $result['content'] . "\n\n";

    // Check if content contains company information
    echo "3. Checking for company-specific content:\n";
    $contentLower = strtolower($result['content']);

    $checks = [
        'alvion' => 'Alvion',
        'global' => 'Global',
        'solutions' => 'Solutions',
        'technology' => 'Technology',
        'ai' => 'AI',
        'machine learning' => 'Machine Learning',
        'voice' => 'Voice'
    ];

    foreach ($checks as $search => $display) {
        if (strpos($contentLower, $search) !== false) {
            echo "✓ Found '$display' in content\n";
        } else {
            echo "✗ '$display' not found in content\n";
        }
    }

    echo "\n4. Content quality assessment:\n";
    $wordCount = str_word_count($result['content']);
    echo "✓ Word count: " . $wordCount . " words\n";

    if ($wordCount > 50) {
        echo "✓ Content has sufficient detail for AI training\n";
    } else {
        echo "⚠ Content might be too brief for effective AI training\n";
    }

} catch (Exception $e) {
    echo "✗ Error occurred: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=======================================================\n";
echo "Test completed.\n";
