<?php

return [
    'enter_item_name' => 'Enter :item name',
    'item_managment' => ':Item management',
    'add_new_item' => 'Add new :item',
    'new_item' => 'New :item',
    'back' => 'Back',
    'item_has_been_added' => ':Item has been added',
    'edit_item_name' => 'Edit :item :name',
    'item_has_been_updated' => ':Item has been updated',
    'items_has_been_updated' => ':Item has been updated',
    'item_has_been_removed' => ':Item has been removed',
    'items_have_been_removed' => ':Item has been removed',
    'item_has_items_associated' => ':Item has items associated. Please remove items associated before deleting  this :item.',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'actions' => 'Actions',
    'no_items' => 'There are no :items...',
    'clear_filters' => 'Clear filters',
    'download_report' => 'Download report',
    'filter' => 'Filter',
    'load_items' => 'Load :item',
    'error' => 'Error :error',
];
