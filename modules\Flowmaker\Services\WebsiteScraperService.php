<?php
namespace Modules\Flowmaker\Services;
require __DIR__.'/../vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;

class WebsiteScraperService
{
    public function extractText(string $url): array
    {
        try {
            // First try with regular HTTP request
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                ])
                ->get($url);

            if (!$response->successful()) {
                Log::error('Failed to fetch website: ' . $url . ' - Status: ' . $response->status());
                return [
                    'title' => '',
                    'url' => $url,
                    'content' => ''
                ];
            }

            $html = $response->body();

            // Check if this is likely a SPA (Single Page Application)
            $isSPA = $this->isSinglePageApp($html);

            if ($isSPA) {
                Log::info('Detected SPA website, using fallback content extraction', ['url' => $url]);
                return $this->extractFromSPA($url, $html);
            }

            // Use DomCrawler for regular websites
            if (class_exists('Symfony\Component\DomCrawler\Crawler')) {
                $crawler = new Crawler($html);

                $title = $crawler->filter('title')->count() > 0
                    ? $crawler->filter('title')->text()
                    : '';

                $paragraphs = $crawler->filter('h1, h2, h3, p')->each(function ($node) {
                    return $node->text();
                });

                $content = $title . "\n" . implode("\n", $paragraphs);
            } else {
                // Fallback to regex-based extraction
                $content = $this->extractWithRegex($html);
            }

            return [
                'title' => $this->extractTitle($html),
                'url' => $url,
                'content' => $content
            ];
        } catch (\Exception $e) {
            Log::error('Error scraping website: ' . $e->getMessage());
            return [
                'title' => '',
                'url' => $url,
                'content' => ''
            ];
        }
    }

    /**
     * Check if the website is likely a Single Page Application
     */
    private function isSinglePageApp(string $html): bool
    {
        // Look for common SPA indicators
        $indicators = [
            'id="root"',
            'id="app"',
            'react',
            'vue',
            'angular',
            'data-reactroot',
            'ng-app',
            'v-app'
        ];

        $htmlLower = strtolower($html);
        foreach ($indicators as $indicator) {
            if (strpos($htmlLower, $indicator) !== false) {
                return true;
            }
        }

        // Check if there's very little content in common HTML tags
        preg_match_all('/<(h[1-6]|p)[^>]*>(.*?)<\/\1>/is', $html, $matches);
        $contentElements = count($matches[0]);

        return $contentElements < 3; // Very few content elements suggests SPA
    }

    /**
     * Extract content from Single Page Applications
     */
    private function extractFromSPA(string $url, string $html): array
    {
        $title = $this->extractTitle($html);

        // For SPAs, we'll create content based on available meta tags and any visible text
        $content = $title;

        // Extract meta description
        preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $metaMatches);
        if (isset($metaMatches[1])) {
            $content .= "\n" . trim($metaMatches[1]);
        }

        // Extract meta keywords if available
        preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $keywordMatches);
        if (isset($keywordMatches[1])) {
            $keywords = trim($keywordMatches[1]);
            if (!empty($keywords)) {
                $content .= "\nKeywords: " . $keywords;
            }
        }

        // Try to extract any visible text from script tags that might contain content
        preg_match_all('/>([^<]+)</i', $html, $textMatches);
        if (isset($textMatches[1])) {
            $visibleTexts = [];
            foreach ($textMatches[1] as $text) {
                $cleanText = trim(strip_tags($text));
                // Only include meaningful text (longer than 10 chars, not just whitespace/symbols)
                if (strlen($cleanText) > 10 && preg_match('/[a-zA-Z]/', $cleanText)) {
                    $visibleTexts[] = $cleanText;
                }
            }

            // Add unique visible texts (limit to avoid too much noise)
            $uniqueTexts = array_unique($visibleTexts);
            $meaningfulTexts = array_slice($uniqueTexts, 0, 10); // Limit to first 10 meaningful texts

            if (!empty($meaningfulTexts)) {
                $content .= "\n" . implode("\n", $meaningfulTexts);
            }
        }

        return [
            'title' => $title,
            'url' => $url,
            'content' => $content
        ];
    }

    /**
     * Extract title from HTML
     */
    private function extractTitle(string $html): string
    {
        preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $titleMatches);
        return isset($titleMatches[1]) ? trim(strip_tags($titleMatches[1])) : '';
    }

    /**
     * Extract content using regex (fallback method)
     */
    private function extractWithRegex(string $html): string
    {
        $title = $this->extractTitle($html);

        // Extract headings and paragraphs
        preg_match_all('/<(h[1-6]|p)[^>]*>(.*?)<\/\1>/is', $html, $contentMatches);
        $contentElements = [];

        if (isset($contentMatches[2])) {
            foreach ($contentMatches[2] as $match) {
                $text = trim(strip_tags($match));
                if (!empty($text) && strlen($text) > 10) {
                    $contentElements[] = $text;
                }
            }
        }

        return $title . "\n" . implode("\n", $contentElements);
    }
} 